'use client';

import ModuleLayout from '@/components/ModuleLayout';
import SmartQuestion from '@/components/SmartQuestion';
import OutputPanel from '@/components/OutputPanel';
import AdvancedOptionsSelector from '@/components/AdvancedOptionsSelector';
import { useContextStore } from '@/store/contextStore';
import {
  PROJECT_TYPES,
  TARGET_PLATFORMS,
  PROGRAMMING_LANGUAGES,
  COMPLEXITY_LEVELS,
  BUDGET_RANGES,
  TEAM_SIZES,
  DEPLOYMENT_TYPES
} from '@/lib/projectOptions';

export default function ProjectDefinition() {
  const { projectDefinition, updateProjectDefinition } = useContextStore();

  const questions = [
    {
      id: 'name',
      question: 'What is the name of your AI project?',
      questionAr: 'ما هو اسم مشروع الذكاء الاصطناعي الخاص بك؟',
      placeholder: 'e.g., Smart Customer Support Bot, Content Generator AI, etc.',
      placeholderAr: 'مثال: بوت دعم العملاء الذكي، مولد المحتوى بالذكاء الاصطناعي، إلخ.',
      type: 'text' as const,
      aiSuggestion: 'Choose a clear, descriptive name that reflects your project\'s main function and target audience.',
      aiSuggestionAr: 'اختر اسماً واضحاً ووصفياً يعكس الوظيفة الرئيسية لمشروعك والجمهور المستهدف.',
      promptTemplate: 'Help me refine this AI project name: "{answer}". Suggest improvements for clarity and market appeal.'
    },
    {
      id: 'purpose',
      question: 'What is the main purpose or problem your AI project aims to solve?',
      questionAr: 'ما هو الهدف الرئيسي أو المشكلة التي يهدف مشروع الذكاء الاصطناعي لحلها؟',
      placeholder: 'Describe the core problem you want to address...',
      placeholderAr: 'صف المشكلة الأساسية التي تريد معالجتها...',
      aiSuggestion: 'Focus on a specific, measurable problem. Avoid being too broad or vague.',
      aiSuggestionAr: 'ركز على مشكلة محددة وقابلة للقياس. تجنب أن تكون عاماً أو غامضاً.',
      promptTemplate: 'Analyze this problem statement for an AI project: "{answer}". Help me make it more specific and actionable.'
    },
    {
      id: 'targetUsers',
      question: 'Who are the primary users or beneficiaries of this project?',
      questionAr: 'من هم المستخدمون الأساسيون أو المستفيدون من هذا المشروع؟',
      placeholder: 'e.g., Customer service teams, Content creators, Students, etc.',
      placeholderAr: 'مثال: فرق خدمة العملاء، منشئو المحتوى، الطلاب، إلخ.',
      aiSuggestion: 'Be specific about user demographics, roles, and their current pain points.',
      aiSuggestionAr: 'كن محدداً حول التركيبة السكانية للمستخدمين وأدوارهم ونقاط الألم الحالية لديهم.',
      promptTemplate: 'Help me create detailed user personas for this target audience: "{answer}". Include their needs and challenges.'
    }
  ];

  const handleFieldChange = (field: keyof typeof projectDefinition, value: string) => {
    updateProjectDefinition({ [field]: value });
  };

  const handleArrayFieldChange = (field: keyof typeof projectDefinition, values: string[]) => {
    updateProjectDefinition({ [field]: values });
  };

  return (
    <ModuleLayout
      title="Project Definition"
      titleAr="تعريف المشروع"
      subtitle="Define the scope, users, and goals of your AI project"
      subtitleAr="حدد نطاق مشروعك والمستخدمين والأهداف"
      emoji="🎯"
      moduleKey="project-definition"
      backLink={{
        href: "/",
        label: "Back to Home",
        labelAr: "العودة للرئيسية"
      }}
      nextLink={{
        href: "/context-map",
        label: "Next: Context Map",
        labelAr: "التالي: خريطة السياق"
      }}
      rightPanel={
        <OutputPanel
          moduleData={projectDefinition}
          moduleName="Project Definition"
          moduleNameAr="تعريف المشروع"
        />
      }
    >
      <div className="space-y-6">
        {/* الأسئلة الأساسية */}
        {questions.map((question) => (
          <SmartQuestion
            key={question.id}
            id={question.id}
            question={question.question}
            questionAr={question.questionAr}
            placeholder={question.placeholder}
            placeholderAr={question.placeholderAr}
            value={projectDefinition[question.id as keyof typeof projectDefinition] || ''}
            onChange={(value) => handleFieldChange(question.id as keyof typeof projectDefinition, value)}
            type={question.type}
            aiSuggestion={question.aiSuggestion}
            aiSuggestionAr={question.aiSuggestionAr}
            promptTemplate={question.promptTemplate}
          />
        ))}

        {/* قسم الخيارات المتقدمة */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 text-center">
            🎯 خيارات التخصيص المتقدمة | Advanced Customization Options
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* نوع المشروع */}
            <AdvancedOptionsSelector
              title="Project Type"
              titleAr="نوع المشروع"
              options={PROJECT_TYPES}
              selectedValues={projectDefinition.projectType ? [projectDefinition.projectType] : []}
              onSelectionChange={(values) => handleFieldChange('projectType', values[0] || '')}
              placeholder="Select project type"
              placeholderAr="اختر نوع المشروع"
            />

            {/* مستوى التعقيد */}
            <AdvancedOptionsSelector
              title="Complexity Level"
              titleAr="مستوى التعقيد"
              options={COMPLEXITY_LEVELS}
              selectedValues={projectDefinition.complexity ? [projectDefinition.complexity] : []}
              onSelectionChange={(values) => handleFieldChange('complexity', values[0] || '')}
              placeholder="Select complexity level"
              placeholderAr="اختر مستوى التعقيد"
            />

            {/* المنصات المستهدفة */}
            <AdvancedOptionsSelector
              title="Target Platforms"
              titleAr="المنصات المستهدفة"
              options={TARGET_PLATFORMS}
              selectedValues={projectDefinition.targetPlatforms || []}
              onSelectionChange={(values) => handleArrayFieldChange('targetPlatforms', values)}
              multiSelect={true}
              placeholder="Select target platforms"
              placeholderAr="اختر المنصات المستهدفة"
            />

            {/* لغات البرمجة الأساسية */}
            <AdvancedOptionsSelector
              title="Primary Programming Languages"
              titleAr="لغات البرمجة الأساسية"
              options={PROGRAMMING_LANGUAGES}
              selectedValues={projectDefinition.primaryLanguages || []}
              onSelectionChange={(values) => handleArrayFieldChange('primaryLanguages', values)}
              multiSelect={true}
              placeholder="Select programming languages"
              placeholderAr="اختر لغات البرمجة"
            />

            {/* حجم الفريق */}
            <AdvancedOptionsSelector
              title="Team Size"
              titleAr="حجم الفريق"
              options={TEAM_SIZES}
              selectedValues={projectDefinition.teamSize ? [projectDefinition.teamSize] : []}
              onSelectionChange={(values) => handleFieldChange('teamSize', values[0] || '')}
              placeholder="Select team size"
              placeholderAr="اختر حجم الفريق"
            />

            {/* نطاق الميزانية */}
            <AdvancedOptionsSelector
              title="Budget Range"
              titleAr="نطاق الميزانية"
              options={BUDGET_RANGES}
              selectedValues={projectDefinition.budget ? [projectDefinition.budget] : []}
              onSelectionChange={(values) => handleFieldChange('budget', values[0] || '')}
              placeholder="Select budget range"
              placeholderAr="اختر نطاق الميزانية"
            />

            {/* نوع النشر */}
            <div className="md:col-span-2">
              <AdvancedOptionsSelector
                title="Deployment Type"
                titleAr="نوع النشر"
                options={DEPLOYMENT_TYPES}
                selectedValues={projectDefinition.deploymentType ? [projectDefinition.deploymentType] : []}
                onSelectionChange={(values) => handleFieldChange('deploymentType', values[0] || '')}
                placeholder="Select deployment type"
                placeholderAr="اختر نوع النشر"
              />
            </div>
          </div>
        </div>
      </div>
    </ModuleLayout>
  );
}
