"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ChevronDown; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdhLE1BQUFBLGFBQXVCO0lBQUM7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBZ0JDLEtBQUs7UUFBUztLQUFFO0NBQUE7QUFhN0UsTUFBQUMsY0FBY0MsZ0VBQWdCQSxDQUFDLGdCQUFnQkoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9jaGV2cm9uLWRvd24udHM/Y2YxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1kb3duJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25Eb3duO1xuIl0sIm5hbWVzIjpbIl9faWNvbk5vZGUiLCJkIiwia2V5IiwiQ2hldnJvbkRvd24iLCJjcmVhdGVMdWNpZGVJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ChevronUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m18 15-6-6-6 6\",\n            key: \"153udz\"\n        }\n    ]\n];\nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-up\", __iconNode);\n //# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi11cC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxNQUFBQSxhQUF1QjtJQUFDO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQWtCQyxLQUFLO1FBQVM7S0FBRTtDQUFBO0FBYS9FLE1BQUFDLFlBQVlDLGdFQUFnQkEsQ0FBQyxjQUFjSiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL2NoZXZyb24tdXAudHM/NWZjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTE4IDE1LTYtNi02IDYnLCBrZXk6ICcxNTN1ZHonIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZXZyb25VcFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TVRnZ01UVXROaTAyTFRZZ05pSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaGV2cm9uLXVwXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvblVwID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi11cCcsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaGV2cm9uVXA7XG4iXSwibmFtZXMiOlsiX19pY29uTm9kZSIsImQiLCJrZXkiLCJDaGV2cm9uVXAiLCJjcmVhdGVMdWNpZGVJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Clock; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 6v6l4 2\",\n            key: \"mmk7yg\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ]\n];\nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"clock\", __iconNode);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Copy; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n];\nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"copy\", __iconNode);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/download.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Download; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 15V3\",\n            key: \"m9g1x1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\",\n            key: \"ih7n3h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m7 10 5 5 5-5\",\n            key: \"brsn70\"\n        }\n    ]\n];\nconst Download = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"download\", __iconNode);\n //# sourceMappingURL=download.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/rocket.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Rocket; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\",\n            key: \"m3kijz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\",\n            key: \"1fmvmk\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\",\n            key: \"1f8sc4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\",\n            key: \"qeys4\"\n        }\n    ]\n];\nconst Rocket = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"rocket\", __iconNode);\n //# sourceMappingURL=rocket.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/triangle-alert.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ TriangleAlert; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\",\n            key: \"wmoenq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n];\nconst TriangleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"triangle-alert\", __iconNode);\n //# sourceMappingURL=triangle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProjectSummaryCard */ \"(app-pages-browser)/./src/components/ProjectSummaryCard.tsx\");\n/* harmony import */ var _components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SmartRecommendations */ \"(app-pages-browser)/./src/components/SmartRecommendations.tsx\");\n/* harmony import */ var _components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProjectStats */ \"(app-pages-browser)/./src/components/ProjectStats.tsx\");\n/* harmony import */ var _components_ProjectRoadmap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ProjectRoadmap */ \"(app-pages-browser)/./src/components/ProjectRoadmap.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_6__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 \".concat(isArabic ? \"font-arabic\" : \"\"),\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? \"Craftery\" : \"Craftery\",\n                    subtitle: isArabic ? \"منصة ذكية مدعومة بالذكاء الاصطناعي لبناء وتطوير الأفكار الإبداعية\" : \"AI-powered Idea Builder\",\n                    emoji: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectRoadmap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDED\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"واجهة متعددة الصفحات\" : \"Multi-page Interface\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"صفحات مخصصة لكل وحدة مع أسئلة ومخرجات مصممة خصيصاً.\" : \"Dedicated pages for each module with tailored prompts and outputs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"✍️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"أسئلة موجهة\" : \"Guided Prompts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أسئلة ذكية لتوجيه عملية تفكيرك عبر كل وحدة.\" : \"Smart questions to guide your thought process through each module.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"نسخ المخرجات\" : \"Output Copying\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أزرار سهلة الاستخدام لنسخ المخرجات الكاملة أو الإجابات الفردية.\" : \"Easy-to-use buttons for copying entire outputs or individual responses.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDFE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"مخرجات منظمة\" : \"Structured Outputs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"مخرجات بصيغة Markdown أو HTML أو JSON للتكامل السهل.\" : \"Outputs in Markdown, HTML, or JSON for easy integration.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"حفظ تلقائي وجلسات\" : \"Auto-save & Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"حفظ واسترجاع تلقائي لجلسات المستخدم للراحة.\" : \"Automatic saving and retrieval of user sessions for convenience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83C\\uDF10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"دعم متعدد اللغات\" : \"Multilingual Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"واجهات باللغتين الإنجليزية والعربية لخدمة جمهور أوسع.\" : \"English and Arabic interfaces to cater to a wider audience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white\",\n                            children: [\n                                \"\\uD83D\\uDEE0️ \",\n                                isArabic ? \"الوحدات المتاحة\" : \"Available Modules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/project-definition\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83C\\uDFAF \",\n                                                isArabic ? \"تعريف المشروع\" : \"Project Definition\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد نطاق مشروعك والمستخدمين والأهداف بأسئلة موجهة.\" : \"Outline the scope, users, and goals of your project with guided prompts.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/interactive\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83E\\uDD16 \",\n                                                isArabic ? \"المساعد التفاعلي\" : \"Interactive Assistant\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تجربة محادثة متقدمة مع الذكاء الاصطناعي مع ميزات تفاعلية حديثة.\" : \"Advanced conversational experience with AI featuring modern interactive capabilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/context-map\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDDFA️ \",\n                                                isArabic ? \"خريطة السياق\" : \"Context Map\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد الوقت واللغة والموقع والجوانب السلوكية لمشروعك.\" : \"Define time, language, location, and behavioral aspects of your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/emotional-tone\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"✨ \",\n                                                isArabic ? \"النبرة والتجربة\" : \"Emotional Tone & Experience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"احدد النبرة العامة وتجربة المستخدم المرغوبة لمشروعك.\" : \"Capture the overall tone and user experience desired for your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/technical-layer\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"⚙️ \",\n                                                isArabic ? \"الطبقة التقنية\" : \"Technical Layer\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد المتطلبات التقنية والأدوات والنماذج المستخدمة.\" : \"Define technical requirements, tools, and models to be used.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/legal-risk\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDD12 \",\n                                                isArabic ? \"التحديات والخصوصية\" : \"Legal & Privacy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تناول التحديات ومخاوف الخصوصية والقضايا التنظيمية.\" : \"Address challenges, privacy concerns, and regulatory issues.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/final-preview\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDCCB \",\n                                                isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"راجع واستخرج السياق الكامل لمشروعك.\" : \"Review and export your complete project context.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/project-definition\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200\",\n                            children: isArabic ? \"ابدأ ببناء السياق\" : \"Start Building Context\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600 dark:text-gray-300\",\n                            children: isArabic ? \"هل أنت مستعد لإنشاء سياق منظم وقابل للتنفيذ لمشاريع الذكاء الاصطناعي؟\" : \"Ready to create structured, actionable context for your AI projects?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_6__.useContextStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ProjectRoadmap.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProjectRoadmap.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProjectRoadmap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,Clock,Copy,Download,Rocket!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,Clock,Copy,Download,Rocket!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,Clock,Copy,Download,Rocket!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,Clock,Copy,Download,Rocket!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,Clock,Copy,Download,Rocket!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,Clock,Copy,Download,Rocket!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronUp,Clock,Copy,Download,Rocket!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProjectRoadmap() {\n    _s();\n    const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [expandedPhases, setExpandedPhases] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"planning\"\n    ]);\n    const [showFullRoadmap, setShowFullRoadmap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تحقق من اكتمال البيانات\n    const checkDataCompleteness = ()=>{\n        const modules = [\n            {\n                name: \"projectDefinition\",\n                data: projectDefinition\n            },\n            {\n                name: \"contextMap\",\n                data: contextMap\n            },\n            {\n                name: \"emotionalTone\",\n                data: emotionalTone\n            },\n            {\n                name: \"technicalLayer\",\n                data: technicalLayer\n            },\n            {\n                name: \"legalRisk\",\n                data: legalRisk\n            }\n        ];\n        let totalFields = 0;\n        let completedFields = 0;\n        modules.forEach((module)=>{\n            const fields = Object.entries(module.data);\n            totalFields += fields.length;\n            fields.forEach((param)=>{\n                let [_, value] = param;\n                if (value) {\n                    if (Array.isArray(value)) {\n                        if (value.length > 0) completedFields++;\n                    } else if (typeof value === \"string\" && value.trim()) {\n                        completedFields++;\n                    }\n                }\n            });\n        });\n        return {\n            completionPercentage: totalFields > 0 ? Math.round(completedFields / totalFields * 100) : 0,\n            isComplete: completionPercentage >= 80\n        };\n    };\n    const { completionPercentage, isComplete } = checkDataCompleteness();\n    // إنشاء خارطة الطريق بناءً على البيانات\n    const generateRoadmap = ()=>{\n        const phases = [];\n        // مرحلة التخطيط والتحليل\n        phases.push({\n            id: \"planning\",\n            title: \"Planning & Analysis\",\n            titleAr: \"التخطيط والتحليل\",\n            description: \"Project setup, requirements analysis, and technical planning\",\n            descriptionAr: \"إعداد المشروع، تحليل المتطلبات، والتخطيط التقني\",\n            duration: \"2-4 weeks\",\n            durationAr: \"2-4 أسابيع\",\n            priority: \"high\",\n            tasks: [\n                {\n                    id: \"requirements\",\n                    title: \"Requirements Documentation\",\n                    titleAr: \"توثيق المتطلبات\",\n                    description: \"Document functional and non-functional requirements\",\n                    descriptionAr: \"توثيق المتطلبات الوظيفية وغير الوظيفية\",\n                    estimatedHours: 40,\n                    skills: [\n                        \"Business Analysis\",\n                        \"Documentation\"\n                    ],\n                    priority: \"high\"\n                },\n                {\n                    id: \"architecture\",\n                    title: \"System Architecture Design\",\n                    titleAr: \"تصميم هندسة النظام\",\n                    description: \"Design overall system architecture and components\",\n                    descriptionAr: \"تصميم الهندسة العامة للنظام والمكونات\",\n                    estimatedHours: 60,\n                    skills: [\n                        \"System Architecture\",\n                        \"Design Patterns\"\n                    ],\n                    priority: \"high\"\n                }\n            ]\n        });\n        // مرحلة التطوير الأساسي\n        if (projectDefinition.projectType) {\n            const developmentTasks = [];\n            if (projectDefinition.projectType === \"web-app\") {\n                developmentTasks.push({\n                    id: \"frontend\",\n                    title: \"Frontend Development\",\n                    titleAr: \"تطوير الواجهة الأمامية\",\n                    description: \"Build user interface and user experience\",\n                    descriptionAr: \"بناء واجهة المستخدم وتجربة المستخدم\",\n                    estimatedHours: 120,\n                    skills: [\n                        \"React\",\n                        \"TypeScript\",\n                        \"CSS\"\n                    ],\n                    priority: \"high\"\n                });\n                developmentTasks.push({\n                    id: \"backend\",\n                    title: \"Backend API Development\",\n                    titleAr: \"تطوير واجهة برمجة التطبيقات الخلفية\",\n                    description: \"Build server-side logic and APIs\",\n                    descriptionAr: \"بناء منطق الخادم وواجهات برمجة التطبيقات\",\n                    estimatedHours: 100,\n                    skills: [\n                        \"Node.js\",\n                        \"Database\",\n                        \"API Design\"\n                    ],\n                    priority: \"high\"\n                });\n            }\n            if (projectDefinition.projectType === \"mobile-app\") {\n                developmentTasks.push({\n                    id: \"mobile-ui\",\n                    title: \"Mobile UI Development\",\n                    titleAr: \"تطوير واجهة التطبيق المحمول\",\n                    description: \"Build mobile user interface\",\n                    descriptionAr: \"بناء واجهة المستخدم للتطبيق المحمول\",\n                    estimatedHours: 150,\n                    skills: [\n                        \"React Native\",\n                        \"Flutter\",\n                        \"Mobile Design\"\n                    ],\n                    priority: \"high\"\n                });\n            }\n            phases.push({\n                id: \"development\",\n                title: \"Core Development\",\n                titleAr: \"التطوير الأساسي\",\n                description: \"Build main application features and functionality\",\n                descriptionAr: \"بناء الميزات والوظائف الرئيسية للتطبيق\",\n                duration: \"8-12 weeks\",\n                durationAr: \"8-12 أسبوع\",\n                priority: \"high\",\n                dependencies: [\n                    \"planning\"\n                ],\n                tasks: developmentTasks\n            });\n        }\n        // مرحلة الأمان والجودة\n        if (technicalLayer.securityRequirements || projectDefinition.complexity === \"enterprise\") {\n            phases.push({\n                id: \"security\",\n                title: \"Security & Quality Assurance\",\n                titleAr: \"الأمان وضمان الجودة\",\n                description: \"Implement security measures and quality testing\",\n                descriptionAr: \"تنفيذ إجراءات الأمان واختبار الجودة\",\n                duration: \"3-5 weeks\",\n                durationAr: \"3-5 أسابيع\",\n                priority: \"high\",\n                dependencies: [\n                    \"development\"\n                ],\n                tasks: [\n                    {\n                        id: \"security-audit\",\n                        title: \"Security Audit\",\n                        titleAr: \"مراجعة الأمان\",\n                        description: \"Comprehensive security testing and vulnerability assessment\",\n                        descriptionAr: \"اختبار أمان شامل وتقييم الثغرات\",\n                        estimatedHours: 40,\n                        skills: [\n                            \"Security Testing\",\n                            \"Penetration Testing\"\n                        ],\n                        priority: \"high\"\n                    },\n                    {\n                        id: \"performance-testing\",\n                        title: \"Performance Testing\",\n                        titleAr: \"اختبار الأداء\",\n                        description: \"Load testing and performance optimization\",\n                        descriptionAr: \"اختبار الأحمال وتحسين الأداء\",\n                        estimatedHours: 30,\n                        skills: [\n                            \"Performance Testing\",\n                            \"Optimization\"\n                        ],\n                        priority: \"medium\"\n                    }\n                ]\n            });\n        }\n        // مرحلة النشر والإطلاق\n        phases.push({\n            id: \"deployment\",\n            title: \"Deployment & Launch\",\n            titleAr: \"النشر والإطلاق\",\n            description: \"Deploy to production and launch the application\",\n            descriptionAr: \"النشر في الإنتاج وإطلاق التطبيق\",\n            duration: \"2-3 weeks\",\n            durationAr: \"2-3 أسابيع\",\n            priority: \"high\",\n            dependencies: [\n                \"development\"\n            ],\n            tasks: [\n                {\n                    id: \"production-setup\",\n                    title: \"Production Environment Setup\",\n                    titleAr: \"إعداد بيئة الإنتاج\",\n                    description: \"Configure production servers and infrastructure\",\n                    descriptionAr: \"تكوين خوادم الإنتاج والبنية التحتية\",\n                    estimatedHours: 25,\n                    skills: [\n                        \"DevOps\",\n                        \"Cloud Infrastructure\"\n                    ],\n                    priority: \"high\"\n                },\n                {\n                    id: \"monitoring-setup\",\n                    title: \"Monitoring & Analytics Setup\",\n                    titleAr: \"إعداد المراقبة والتحليلات\",\n                    description: \"Set up monitoring, logging, and analytics\",\n                    descriptionAr: \"إعداد المراقبة والتسجيل والتحليلات\",\n                    estimatedHours: 20,\n                    skills: [\n                        \"Monitoring Tools\",\n                        \"Analytics\"\n                    ],\n                    priority: \"medium\"\n                }\n            ]\n        });\n        return phases;\n    };\n    const roadmapPhases = generateRoadmap();\n    const togglePhase = (phaseId)=>{\n        setExpandedPhases((prev)=>prev.includes(phaseId) ? prev.filter((id)=>id !== phaseId) : [\n                ...prev,\n                phaseId\n            ]);\n    };\n    const calculateTotalEstimate = ()=>{\n        let totalHours = 0;\n        let totalWeeks = 0;\n        roadmapPhases.forEach((phase)=>{\n            phase.tasks.forEach((task)=>{\n                totalHours += task.estimatedHours;\n            });\n            // استخراج الأسابيع من النص\n            const weekMatch = phase.duration.match(/(\\d+)-?(\\d+)?/);\n            if (weekMatch) {\n                const minWeeks = parseInt(weekMatch[1]);\n                const maxWeeks = weekMatch[2] ? parseInt(weekMatch[2]) : minWeeks;\n                totalWeeks += (minWeeks + maxWeeks) / 2;\n            }\n        });\n        return {\n            totalHours,\n            totalWeeks\n        };\n    };\n    const { totalHours, totalWeeks } = calculateTotalEstimate();\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"text-red-600 dark:text-red-400\";\n            case \"medium\":\n                return \"text-yellow-600 dark:text-yellow-400\";\n            case \"low\":\n                return \"text-green-600 dark:text-green-400\";\n            default:\n                return \"text-gray-600 dark:text-gray-400\";\n        }\n    };\n    const getPriorityBg = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800\";\n            case \"medium\":\n                return \"bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800\";\n            case \"low\":\n                return \"bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-800\";\n            default:\n                return \"bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700\";\n        }\n    };\n    if (!isComplete) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-8 h-8 text-orange-600 dark:text-orange-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isArabic ? \"\\uD83D\\uDDFA️ خارطة الطريق غير متوفرة\" : \"\\uD83D\\uDDFA️ Roadmap Not Available\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                        children: isArabic ? \"يرجى إكمال المزيد من البيانات لإنشاء خارطة طريق شاملة. التقدم الحالي: \".concat(completionPercentage, \"%\") : \"Please complete more data to generate a comprehensive roadmap. Current progress: \".concat(completionPercentage, \"%\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-500 h-3 rounded-full transition-all duration-500\",\n                            style: {\n                                width: \"\".concat(completionPercentage, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                        children: isArabic ? \"يتطلب 80% على الأقل من البيانات لإنشاء خارطة الطريق\" : \"Requires at least 80% data completion to generate roadmap\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n            lineNumber: 335,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-emerald-50 to-teal-100 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-xl border border-emerald-200 dark:border-emerald-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-emerald-200 dark:border-emerald-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-emerald-100 dark:bg-emerald-800 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6 text-emerald-600 dark:text-emerald-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: isArabic ? \"text-right\" : \"text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                children: isArabic ? \"\\uD83D\\uDDFA️ خارطة طريق المشروع\" : \"\\uD83D\\uDDFA️ Project Roadmap\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: isArabic ? \"خطة تنفيذ شاملة مخصصة لمشروعك\" : \"Comprehensive execution plan tailored for your project\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowFullRoadmap(!showFullRoadmap),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-emerald-100 dark:bg-emerald-800 text-emerald-700 dark:text-emerald-300 rounded-lg hover:bg-emerald-200 dark:hover:bg-emerald-700 transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    showFullRoadmap ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 68\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: isArabic ? showFullRoadmap ? \"إخفاء التفاصيل\" : \"عرض التفاصيل\" : showFullRoadmap ? \"Hide Details\" : \"Show Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-emerald-600 dark:text-emerald-400\",\n                                        children: roadmapPhases.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                        children: isArabic ? \"مراحل\" : \"Phases\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-blue-600 dark:text-blue-400\",\n                                        children: Math.round(totalWeeks)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                        children: isArabic ? \"أسابيع\" : \"Weeks\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-purple-600 dark:text-purple-400\",\n                                        children: totalHours\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                        children: isArabic ? \"ساعات\" : \"Hours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-orange-600 dark:text-orange-400\",\n                                        children: [\n                                            completionPercentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                        children: isArabic ? \"جاهزية\" : \"Ready\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            showFullRoadmap && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: roadmapPhases.map((phase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg \".concat(getPriorityBg(phase.priority)),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 cursor-pointer\",\n                                        onClick: ()=>togglePhase(phase.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-white dark:bg-gray-800 rounded-full font-bold text-sm\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: isArabic ? \"text-right\" : \"text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900 dark:text-gray-100\",\n                                                                    children: isArabic ? phase.titleAr : phase.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                    children: isArabic ? phase.descriptionAr : phase.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"w-3 h-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                            lineNumber: 465,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: isArabic ? phase.durationAr : phase.duration\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                            lineNumber: 466,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this),\n                                                expandedPhases.includes(phase.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this),\n                                    expandedPhases.includes(phase.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 pb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: phase.tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                        children: isArabic ? task.titleAr : task.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                        children: isArabic ? task.descriptionAr : task.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    task.estimatedHours,\n                                                                                    \" \",\n                                                                                    isArabic ? \"ساعات\" : \"hours\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                                lineNumber: 497,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1\",\n                                                                                children: task.skills.slice(0, 2).map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded\",\n                                                                                        children: skill\n                                                                                    }, skill, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                                        lineNumber: 502,\n                                                                                        columnNumber: 37\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded \".concat(getPriorityColor(task.priority)),\n                                                                children: task.priority\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, task.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, phase.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 pt-4 border-t border-emerald-200 dark:border-emerald-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: isArabic ? \"تحميل خارطة الطريق\" : \"Download Roadmap\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronUp_Clock_Copy_Download_Rocket_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: isArabic ? \"نسخ كنص\" : \"Copy as Text\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n                lineNumber: 441,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectRoadmap.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectRoadmap, \"vBuQUtIo0P8cwnnmZYFbZy3n5kY=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = ProjectRoadmap;\nvar _c;\n$RefreshReg$(_c, \"ProjectRoadmap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProjectRoadmap.tsx\n"));

/***/ })

});