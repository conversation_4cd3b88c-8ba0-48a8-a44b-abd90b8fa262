'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';
import { 
  PROJECT_TYPES, 
  TARGET_PLATFORMS, 
  PROGRAMMING_LANGUAGES, 
  COMPLEXITY_LEVELS,
  BUDGET_RANGES,
  TEAM_SIZES,
  DEPLOYMENT_TYPES
} from '@/lib/projectOptions';
import { 
  ARCHITECTURE_PATTERNS,
  SCALING_STRATEGIES,
  SECURITY_REQUIREMENTS,
  PERFORMANCE_TARGETS,
  INTEGRATION_NEEDS,
  MONITORING_TOOLS
} from '@/lib/technicalOptions';
import { 
  Calendar, 
  Users, 
  Code, 
  Database, 
  Shield, 
  Zap, 
  GitBranch, 
  Rocket,
  CheckCircle,
  Clock,
  AlertTriangle,
  Download,
  Copy,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface RoadmapPhase {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  duration: string;
  durationAr: string;
  tasks: RoadmapTask[];
  priority: 'high' | 'medium' | 'low';
  dependencies?: string[];
}

interface RoadmapTask {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  estimatedHours: number;
  skills: string[];
  priority: 'high' | 'medium' | 'low';
}

export default function ProjectRoadmap() {
  const { 
    projectDefinition, 
    contextMap, 
    emotionalTone, 
    technicalLayer, 
    legalRisk, 
    currentLanguage 
  } = useContextStore();
  
  const [expandedPhases, setExpandedPhases] = useState<string[]>(['planning']);
  const [showFullRoadmap, setShowFullRoadmap] = useState(false);
  const isArabic = currentLanguage === 'ar';

  // تحقق من اكتمال البيانات
  const checkDataCompleteness = () => {
    const modules = [
      { name: 'projectDefinition', data: projectDefinition },
      { name: 'contextMap', data: contextMap },
      { name: 'emotionalTone', data: emotionalTone },
      { name: 'technicalLayer', data: technicalLayer },
      { name: 'legalRisk', data: legalRisk }
    ];

    let totalFields = 0;
    let completedFields = 0;

    modules.forEach(module => {
      const fields = Object.entries(module.data);
      totalFields += fields.length;
      
      fields.forEach(([_, value]) => {
        if (value) {
          if (Array.isArray(value)) {
            if (value.length > 0) completedFields++;
          } else if (typeof value === 'string' && value.trim()) {
            completedFields++;
          }
        }
      });
    });

    return {
      completionPercentage: totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0,
      isComplete: completionPercentage >= 80
    };
  };

  const { completionPercentage, isComplete } = checkDataCompleteness();

  // إنشاء خارطة الطريق بناءً على البيانات
  const generateRoadmap = (): RoadmapPhase[] => {
    const phases: RoadmapPhase[] = [];

    // مرحلة التخطيط والتحليل
    phases.push({
      id: 'planning',
      title: 'Planning & Analysis',
      titleAr: 'التخطيط والتحليل',
      description: 'Project setup, requirements analysis, and technical planning',
      descriptionAr: 'إعداد المشروع، تحليل المتطلبات، والتخطيط التقني',
      duration: '2-4 weeks',
      durationAr: '2-4 أسابيع',
      priority: 'high',
      tasks: [
        {
          id: 'requirements',
          title: 'Requirements Documentation',
          titleAr: 'توثيق المتطلبات',
          description: 'Document functional and non-functional requirements',
          descriptionAr: 'توثيق المتطلبات الوظيفية وغير الوظيفية',
          estimatedHours: 40,
          skills: ['Business Analysis', 'Documentation'],
          priority: 'high'
        },
        {
          id: 'architecture',
          title: 'System Architecture Design',
          titleAr: 'تصميم هندسة النظام',
          description: 'Design overall system architecture and components',
          descriptionAr: 'تصميم الهندسة العامة للنظام والمكونات',
          estimatedHours: 60,
          skills: ['System Architecture', 'Design Patterns'],
          priority: 'high'
        }
      ]
    });

    // مرحلة التطوير الأساسي
    if (projectDefinition.projectType) {
      const developmentTasks: RoadmapTask[] = [];
      
      if (projectDefinition.projectType === 'web-app') {
        developmentTasks.push({
          id: 'frontend',
          title: 'Frontend Development',
          titleAr: 'تطوير الواجهة الأمامية',
          description: 'Build user interface and user experience',
          descriptionAr: 'بناء واجهة المستخدم وتجربة المستخدم',
          estimatedHours: 120,
          skills: ['React', 'TypeScript', 'CSS'],
          priority: 'high'
        });
        
        developmentTasks.push({
          id: 'backend',
          title: 'Backend API Development',
          titleAr: 'تطوير واجهة برمجة التطبيقات الخلفية',
          description: 'Build server-side logic and APIs',
          descriptionAr: 'بناء منطق الخادم وواجهات برمجة التطبيقات',
          estimatedHours: 100,
          skills: ['Node.js', 'Database', 'API Design'],
          priority: 'high'
        });
      }

      if (projectDefinition.projectType === 'mobile-app') {
        developmentTasks.push({
          id: 'mobile-ui',
          title: 'Mobile UI Development',
          titleAr: 'تطوير واجهة التطبيق المحمول',
          description: 'Build mobile user interface',
          descriptionAr: 'بناء واجهة المستخدم للتطبيق المحمول',
          estimatedHours: 150,
          skills: ['React Native', 'Flutter', 'Mobile Design'],
          priority: 'high'
        });
      }

      phases.push({
        id: 'development',
        title: 'Core Development',
        titleAr: 'التطوير الأساسي',
        description: 'Build main application features and functionality',
        descriptionAr: 'بناء الميزات والوظائف الرئيسية للتطبيق',
        duration: '8-12 weeks',
        durationAr: '8-12 أسبوع',
        priority: 'high',
        dependencies: ['planning'],
        tasks: developmentTasks
      });
    }

    // مرحلة الأمان والجودة
    if (technicalLayer.securityRequirements || projectDefinition.complexity === 'enterprise') {
      phases.push({
        id: 'security',
        title: 'Security & Quality Assurance',
        titleAr: 'الأمان وضمان الجودة',
        description: 'Implement security measures and quality testing',
        descriptionAr: 'تنفيذ إجراءات الأمان واختبار الجودة',
        duration: '3-5 weeks',
        durationAr: '3-5 أسابيع',
        priority: 'high',
        dependencies: ['development'],
        tasks: [
          {
            id: 'security-audit',
            title: 'Security Audit',
            titleAr: 'مراجعة الأمان',
            description: 'Comprehensive security testing and vulnerability assessment',
            descriptionAr: 'اختبار أمان شامل وتقييم الثغرات',
            estimatedHours: 40,
            skills: ['Security Testing', 'Penetration Testing'],
            priority: 'high'
          },
          {
            id: 'performance-testing',
            title: 'Performance Testing',
            titleAr: 'اختبار الأداء',
            description: 'Load testing and performance optimization',
            descriptionAr: 'اختبار الأحمال وتحسين الأداء',
            estimatedHours: 30,
            skills: ['Performance Testing', 'Optimization'],
            priority: 'medium'
          }
        ]
      });
    }

    // مرحلة النشر والإطلاق
    phases.push({
      id: 'deployment',
      title: 'Deployment & Launch',
      titleAr: 'النشر والإطلاق',
      description: 'Deploy to production and launch the application',
      descriptionAr: 'النشر في الإنتاج وإطلاق التطبيق',
      duration: '2-3 weeks',
      durationAr: '2-3 أسابيع',
      priority: 'high',
      dependencies: ['development'],
      tasks: [
        {
          id: 'production-setup',
          title: 'Production Environment Setup',
          titleAr: 'إعداد بيئة الإنتاج',
          description: 'Configure production servers and infrastructure',
          descriptionAr: 'تكوين خوادم الإنتاج والبنية التحتية',
          estimatedHours: 25,
          skills: ['DevOps', 'Cloud Infrastructure'],
          priority: 'high'
        },
        {
          id: 'monitoring-setup',
          title: 'Monitoring & Analytics Setup',
          titleAr: 'إعداد المراقبة والتحليلات',
          description: 'Set up monitoring, logging, and analytics',
          descriptionAr: 'إعداد المراقبة والتسجيل والتحليلات',
          estimatedHours: 20,
          skills: ['Monitoring Tools', 'Analytics'],
          priority: 'medium'
        }
      ]
    });

    return phases;
  };

  const roadmapPhases = generateRoadmap();

  const togglePhase = (phaseId: string) => {
    setExpandedPhases(prev => 
      prev.includes(phaseId) 
        ? prev.filter(id => id !== phaseId)
        : [...prev, phaseId]
    );
  };

  const calculateTotalEstimate = () => {
    let totalHours = 0;
    let totalWeeks = 0;
    
    roadmapPhases.forEach(phase => {
      phase.tasks.forEach(task => {
        totalHours += task.estimatedHours;
      });
      
      // استخراج الأسابيع من النص
      const weekMatch = phase.duration.match(/(\d+)-?(\d+)?/);
      if (weekMatch) {
        const minWeeks = parseInt(weekMatch[1]);
        const maxWeeks = weekMatch[2] ? parseInt(weekMatch[2]) : minWeeks;
        totalWeeks += (minWeeks + maxWeeks) / 2;
      }
    });

    return { totalHours, totalWeeks };
  };

  const { totalHours, totalWeeks } = calculateTotalEstimate();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 dark:text-red-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-green-600 dark:text-green-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPriorityBg = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
      case 'low': return 'bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      default: return 'bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700';
    }
  };

  if (!isComplete) {
    return (
      <div className="bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800">
        <div className={`text-center ${isArabic ? 'text-right' : 'text-left'}`}>
          <div className="flex items-center justify-center mb-4">
            <AlertTriangle className="w-8 h-8 text-orange-600 dark:text-orange-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            {isArabic ? '🗺️ خارطة الطريق غير متوفرة' : '🗺️ Roadmap Not Available'}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {isArabic 
              ? `يرجى إكمال المزيد من البيانات لإنشاء خارطة طريق شاملة. التقدم الحالي: ${completionPercentage}%`
              : `Please complete more data to generate a comprehensive roadmap. Current progress: ${completionPercentage}%`
            }
          </p>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
            <div 
              className="bg-orange-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {isArabic 
              ? 'يتطلب 80% على الأقل من البيانات لإنشاء خارطة الطريق'
              : 'Requires at least 80% data completion to generate roadmap'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-emerald-50 to-teal-100 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-xl border border-emerald-200 dark:border-emerald-800">
      {/* Header */}
      <div className="p-6 border-b border-emerald-200 dark:border-emerald-700">
        <div className={`flex items-center justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-emerald-100 dark:bg-emerald-800 rounded-lg">
              <Rocket className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div className={isArabic ? 'text-right' : 'text-left'}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {isArabic ? '🗺️ خارطة طريق المشروع' : '🗺️ Project Roadmap'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {isArabic 
                  ? 'خطة تنفيذ شاملة مخصصة لمشروعك'
                  : 'Comprehensive execution plan tailored for your project'
                }
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setShowFullRoadmap(!showFullRoadmap)}
            className={`flex items-center gap-2 px-4 py-2 bg-emerald-100 dark:bg-emerald-800 text-emerald-700 dark:text-emerald-300 rounded-lg hover:bg-emerald-200 dark:hover:bg-emerald-700 transition-colors ${isArabic ? 'flex-row-reverse' : ''}`}
          >
            {showFullRoadmap ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            <span className="text-sm font-medium">
              {isArabic ? (showFullRoadmap ? 'إخفاء التفاصيل' : 'عرض التفاصيل') : (showFullRoadmap ? 'Hide Details' : 'Show Details')}
            </span>
          </button>
        </div>

        {/* ملخص المشروع */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400">
              {roadmapPhases.length}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {isArabic ? 'مراحل' : 'Phases'}
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {Math.round(totalWeeks)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {isArabic ? 'أسابيع' : 'Weeks'}
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
              {totalHours}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {isArabic ? 'ساعات' : 'Hours'}
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
              {completionPercentage}%
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {isArabic ? 'جاهزية' : 'Ready'}
            </div>
          </div>
        </div>
      </div>

      {/* المراحل */}
      {showFullRoadmap && (
        <div className="p-6">
          <div className="space-y-4">
            {roadmapPhases.map((phase, index) => (
              <div
                key={phase.id}
                className={`border rounded-lg ${getPriorityBg(phase.priority)}`}
              >
                <div 
                  className="p-4 cursor-pointer"
                  onClick={() => togglePhase(phase.id)}
                >
                  <div className={`flex items-center justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
                      <div className="flex items-center justify-center w-8 h-8 bg-white dark:bg-gray-800 rounded-full font-bold text-sm">
                        {index + 1}
                      </div>
                      <div className={isArabic ? 'text-right' : 'text-left'}>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                          {isArabic ? phase.titleAr : phase.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {isArabic ? phase.descriptionAr : phase.description}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock className="w-3 h-3 text-gray-400" />
                          <span className="text-xs text-gray-500">
                            {isArabic ? phase.durationAr : phase.duration}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {expandedPhases.includes(phase.id) ? 
                      <ChevronUp className="w-5 h-5 text-gray-500" /> : 
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    }
                  </div>
                </div>

                {expandedPhases.includes(phase.id) && (
                  <div className="px-4 pb-4">
                    <div className="space-y-3">
                      {phase.tasks.map((task) => (
                        <div
                          key={task.id}
                          className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700"
                        >
                          <div className={`flex items-start justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
                            <div className={`flex-1 ${isArabic ? 'text-right' : 'text-left'}`}>
                              <h5 className="font-medium text-gray-900 dark:text-gray-100">
                                {isArabic ? task.titleAr : task.title}
                              </h5>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {isArabic ? task.descriptionAr : task.description}
                              </p>
                              <div className="flex items-center gap-4 mt-2">
                                <span className="text-xs text-gray-500">
                                  {task.estimatedHours} {isArabic ? 'ساعات' : 'hours'}
                                </span>
                                <div className="flex gap-1">
                                  {task.skills.slice(0, 2).map((skill) => (
                                    <span
                                      key={skill}
                                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded"
                                    >
                                      {skill}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                            <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(task.priority)}`}>
                              {task.priority}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* أزرار التصدير */}
          <div className="mt-6 pt-4 border-t border-emerald-200 dark:border-emerald-700">
            <div className={`flex gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
              <button className={`flex items-center gap-2 px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors ${isArabic ? 'flex-row-reverse' : ''}`}>
                <Download className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {isArabic ? 'تحميل خارطة الطريق' : 'Download Roadmap'}
                </span>
              </button>
              
              <button className={`flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ${isArabic ? 'flex-row-reverse' : ''}`}>
                <Copy className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {isArabic ? 'نسخ كنص' : 'Copy as Text'}
                </span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
