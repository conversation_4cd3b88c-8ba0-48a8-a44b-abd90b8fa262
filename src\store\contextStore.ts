import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// تعريف أنواع البيانات لكل محور
export interface ProjectDefinition {
  name: string;
  purpose: string;
  targetUsers: string;
  goals: string;
  scope: string;
  timeline: string;
  // خيارات التخصيص المتقدمة
  projectType: string; // نوع المشروع (تطبيق ويب، تطبيق جوال، سطح المكتب، إلخ)
  targetPlatforms: string[]; // المنصات المستهدفة
  primaryLanguages: string[]; // لغات البرمجة الأساسية
  complexity: string; // مستوى التعقيد
  budget: string; // الميزانية المتوقعة
  teamSize: string; // حجم الفريق
  deploymentType: string; // نوع النشر (سحابي، محلي، هجين)
}

export interface ContextMap {
  timeContext: string;
  language: string;
  location: string;
  culturalContext: string;
  behavioralAspects: string;
  environmentalFactors: string;
}

export interface EmotionalTone {
  personality: string;
  communicationStyle: string;
  userExperience: string;
  brandVoice: string;
  emotionalIntelligence: string;
  interactionFlow: string;
}

export interface TechnicalLayer {
  programmingLanguages: string;
  frameworks: string;
  llmModels: string;
  databases: string;
  apis: string;
  infrastructure: string;
  // خيارات تقنية متقدمة
  architecturePattern: string; // نمط الهندسة المعمارية
  scalingStrategy: string; // استراتيجية التوسع
  securityRequirements: string; // متطلبات الأمان
  performanceTargets: string; // أهداف الأداء
  integrationNeeds: string; // احتياجات التكامل
  monitoringTools: string; // أدوات المراقبة
}

export interface LegalRisk {
  privacyConcerns: string;
  dataProtection: string;
  compliance: string;
  risks: string;
  mitigation: string;
  ethicalConsiderations: string;
}

// LLM Provider interfaces
export interface LLMModel {
  id: string;
  name: string;
  description: string;
  contextLength: number;
  pricing?: string;
  inputPrice?: number;
  outputPrice?: number;
}

export interface LLMProvider {
  id: string;
  name: string;
  icon: string;
  description: string;
  baseUrl: string;
  apiKeyPlaceholder: string;
  models: LLMModel[];
  headers?: Record<string, string>;
  isActive: boolean;
  supportsStreaming?: boolean;
  maxTokens?: number;
}

export interface ProviderConfig {
  id: string;
  apiKey: string;
  baseUrl?: string; // Override default base URL
  selectedModels: string[];
  isEnabled: boolean;
  lastValidated?: Date;
  validationStatus: 'pending' | 'valid' | 'invalid' | 'error';
  errorMessage?: string;
  customSettings?: Record<string, any>;

  // ميزات متقدمة
  priority: number; // أولوية المزود (1-10)
  isBackup: boolean; // هل هو مزود احتياطي
  maxRequestsPerMinute?: number; // حد الطلبات في الدقيقة
  timeout?: number; // مهلة الطلب بالثواني
  retryAttempts?: number; // عدد محاولات الإعادة
  costPerToken?: number; // تكلفة كل token

  // إحصائيات الأداء
  stats?: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    totalTokensUsed: number;
    totalCost: number;
    lastUsed?: Date;
  };
}

export interface ApiSettings {
  providers: ProviderConfig[];
  defaultProvider?: string;
  defaultModel?: string;
  globalSettings: {
    temperature: number;
    topP: number;
    maxTokens: number;
    timeout: number;
  };
  // Legacy support - will be migrated
  openaiApiKey?: string;
  openrouterApiKey?: string;
  customModels?: any[];
}

// حالة التطبيق الرئيسية
export interface ContextState {
  // بيانات كل محور
  projectDefinition: ProjectDefinition;
  contextMap: ContextMap;
  emotionalTone: EmotionalTone;
  technicalLayer: TechnicalLayer;
  legalRisk: LegalRisk;

  // إعدادات التطبيق
  currentLanguage: 'ar' | 'en';
  outputFormat: 'markdown' | 'html' | 'json' | 'yaml';
  apiSettings: ApiSettings;
  
  // الإجراءات
  updateProjectDefinition: (data: Partial<ProjectDefinition>) => void;
  updateContextMap: (data: Partial<ContextMap>) => void;
  updateEmotionalTone: (data: Partial<EmotionalTone>) => void;
  updateTechnicalLayer: (data: Partial<TechnicalLayer>) => void;
  updateLegalRisk: (data: Partial<LegalRisk>) => void;
  setLanguage: (lang: 'ar' | 'en') => void;
  setOutputFormat: (format: 'markdown' | 'html' | 'json' | 'yaml') => void;
  setApiSettings: (settings: ApiSettings) => void;
  clearAllAnswers: () => void;

  // LLM Provider management
  addProvider: (provider: ProviderConfig) => void;
  updateProvider: (providerId: string, updates: Partial<ProviderConfig>) => void;
  removeProvider: (providerId: string) => void;
  validateProvider: (providerId: string) => Promise<boolean>;
  getProvider: (providerId: string) => ProviderConfig | undefined;
  getActiveProviders: () => ProviderConfig[];
  setDefaultProvider: (providerId: string) => void;

  // ميزات متقدمة للمزودين
  getProvidersByPriority: () => ProviderConfig[];
  getBackupProviders: () => ProviderConfig[];
  updateProviderStats: (id: string, stats: Partial<ProviderConfig['stats']>) => void;
  getBestProvider: (criteria?: 'speed' | 'cost' | 'reliability') => ProviderConfig | undefined;

  resetAll: () => void;
  
  // مساعدات للتصدير
  getModuleData: (module: string) => any;
  getAllData: () => any;
}

// القيم الافتراضية
const initialState = {
  projectDefinition: {
    name: '',
    purpose: '',
    targetUsers: '',
    goals: '',
    scope: '',
    timeline: '',
    projectType: '',
    targetPlatforms: [],
    primaryLanguages: [],
    complexity: '',
    budget: '',
    teamSize: '',
    deploymentType: ''
  },
  contextMap: {
    timeContext: '',
    language: '',
    location: '',
    culturalContext: '',
    behavioralAspects: '',
    environmentalFactors: ''
  },
  emotionalTone: {
    personality: '',
    communicationStyle: '',
    userExperience: '',
    brandVoice: '',
    emotionalIntelligence: '',
    interactionFlow: ''
  },
  technicalLayer: {
    programmingLanguages: '',
    frameworks: '',
    llmModels: '',
    databases: '',
    apis: '',
    infrastructure: '',
    architecturePattern: '',
    scalingStrategy: '',
    securityRequirements: '',
    performanceTargets: '',
    integrationNeeds: '',
    monitoringTools: ''
  },
  legalRisk: {
    privacyConcerns: '',
    dataProtection: '',
    compliance: '',
    risks: '',
    mitigation: '',
    ethicalConsiderations: ''
  },
  currentLanguage: 'ar' as const,
  outputFormat: 'markdown' as const,
  apiSettings: {
    providers: [],
    globalSettings: {
      temperature: 0.7,
      topP: 0.9,
      maxTokens: 1000,
      timeout: 30000
    },
    // Legacy support
    openaiApiKey: '',
    openrouterApiKey: '',
    customModels: []
  }
};

// إنشاء المتجر مع التخزين المستمر
export const useContextStore = create<ContextState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      updateProjectDefinition: (data) =>
        set((state) => ({
          projectDefinition: { ...state.projectDefinition, ...data }
        })),
        
      updateContextMap: (data) =>
        set((state) => ({
          contextMap: { ...state.contextMap, ...data }
        })),
        
      updateEmotionalTone: (data) =>
        set((state) => ({
          emotionalTone: { ...state.emotionalTone, ...data }
        })),
        
      updateTechnicalLayer: (data) =>
        set((state) => ({
          technicalLayer: { ...state.technicalLayer, ...data }
        })),
        
      updateLegalRisk: (data) =>
        set((state) => ({
          legalRisk: { ...state.legalRisk, ...data }
        })),
        
      setLanguage: (lang) => set({ currentLanguage: lang }),
      setOutputFormat: (format) => set({ outputFormat: format }),
      setApiSettings: (settings) => set({
        apiSettings: {
          ...settings,
          providers: settings.providers || []
        }
      }),

      // LLM Provider management functions
      addProvider: (provider) =>
        set((state) => {
          const existingProviders = state.apiSettings.providers || [];
          // تحقق من عدم وجود مقدم خدمة بنفس المعرف
          if (existingProviders.find(p => p.id === provider.id)) {
            console.warn(`Provider with id ${provider.id} already exists`);
            return state; // لا تضيف مقدم خدمة مكرر
          }

          // إضافة القيم الافتراضية للميزات المتقدمة
          const enhancedProvider: ProviderConfig = {
            ...provider,
            priority: provider.priority || 5,
            isBackup: provider.isBackup || false,
            maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,
            timeout: provider.timeout || 30,
            retryAttempts: provider.retryAttempts || 3,
            stats: provider.stats || {
              totalRequests: 0,
              successfulRequests: 0,
              failedRequests: 0,
              averageResponseTime: 0,
              totalTokensUsed: 0,
              totalCost: 0
            }
          };

          return {
            apiSettings: {
              ...state.apiSettings,
              providers: [...existingProviders, enhancedProvider]
            }
          };
        }),

      updateProvider: (providerId, updates) =>
        set((state) => ({
          apiSettings: {
            ...state.apiSettings,
            providers: (state.apiSettings.providers || []).map(p =>
              p.id === providerId ? { ...p, ...updates } : p
            )
          }
        })),

      removeProvider: (providerId) =>
        set((state) => ({
          apiSettings: {
            ...state.apiSettings,
            providers: (state.apiSettings.providers || []).filter(p => p.id !== providerId),
            defaultProvider: state.apiSettings.defaultProvider === providerId
              ? undefined
              : state.apiSettings.defaultProvider
          }
        })),

      validateProvider: async (providerId) => {
        const state = get();
        const provider = (state.apiSettings.providers || []).find(p => p.id === providerId);
        if (!provider) return false;

        try {
          // Update status to pending
          state.updateProvider(providerId, {
            validationStatus: 'pending',
            errorMessage: undefined
          });

          // Call validation API
          const response = await fetch('/api/llm/validate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              providerId: provider.id,
              apiKey: provider.apiKey,
              baseUrl: provider.baseUrl
            })
          });

          const result = await response.json();

          if (result.valid) {
            state.updateProvider(providerId, {
              validationStatus: 'valid',
              lastValidated: new Date(),
              errorMessage: undefined,
              isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق
            });
            return true;
          } else {
            state.updateProvider(providerId, {
              validationStatus: 'invalid',
              errorMessage: result.error || 'Validation failed'
            });
            return false;
          }
        } catch (error) {
          state.updateProvider(providerId, {
            validationStatus: 'error',
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          });
          return false;
        }
      },

      getProvider: (providerId) => {
        const state = get();
        return (state.apiSettings.providers || []).find(p => p.id === providerId);
      },

      getActiveProviders: () => {
        const state = get();
        return (state.apiSettings.providers || []).filter(p => p.isEnabled);
      },

      setDefaultProvider: (providerId) =>
        set((state) => ({
          apiSettings: {
            ...state.apiSettings,
            defaultProvider: providerId
          }
        })),

      // ميزات متقدمة للمزودين
      getProvidersByPriority: () => {
        const state = get();
        return (state.apiSettings.providers || [])
          .filter(p => p.isEnabled)
          .sort((a, b) => (b.priority || 5) - (a.priority || 5));
      },

      getBackupProviders: () => {
        const state = get();
        return (state.apiSettings.providers || [])
          .filter(p => p.isEnabled && p.isBackup);
      },

      updateProviderStats: (id, stats) =>
        set((state) => {
          const providers = state.apiSettings.providers || [];
          const providerIndex = providers.findIndex(p => p.id === id);

          if (providerIndex !== -1) {
            const updatedProviders = [...providers];
            updatedProviders[providerIndex] = {
              ...updatedProviders[providerIndex],
              stats: {
                ...updatedProviders[providerIndex].stats,
                ...stats,
                lastUsed: new Date()
              }
            };

            return {
              apiSettings: {
                ...state.apiSettings,
                providers: updatedProviders
              }
            };
          }
          return state;
        }),

      getBestProvider: (criteria = 'reliability') => {
        const state = get();
        const activeProviders = (state.apiSettings.providers || [])
          .filter(p => p.isEnabled && !p.isBackup);

        if (activeProviders.length === 0) return undefined;

        switch (criteria) {
          case 'speed':
            return activeProviders.reduce((best, current) => {
              const bestSpeed = best.stats?.averageResponseTime || Infinity;
              const currentSpeed = current.stats?.averageResponseTime || Infinity;
              return currentSpeed < bestSpeed ? current : best;
            });

          case 'cost':
            return activeProviders.reduce((best, current) => {
              const bestCost = best.costPerToken || Infinity;
              const currentCost = current.costPerToken || Infinity;
              return currentCost < bestCost ? current : best;
            });

          case 'reliability':
          default:
            return activeProviders.reduce((best, current) => {
              const bestReliability = best.stats ?
                (best.stats.successfulRequests / (best.stats.totalRequests || 1)) : 0;
              const currentReliability = current.stats ?
                (current.stats.successfulRequests / (current.stats.totalRequests || 1)) : 0;
              return currentReliability > bestReliability ? current : best;
            });
        }
      },

      resetAll: () => set(initialState),

      // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)
      clearAllAnswers: () => set((state) => ({
        ...state,
        projectDefinition: {
          name: '',
          purpose: '',
          targetUsers: '',
          goals: '',
          scope: '',
          timeline: ''
        },
        contextMap: {
          timeContext: '',
          language: '',
          location: '',
          culturalContext: '',
          behavioralAspects: '',
          environmentalFactors: ''
        },
        emotionalTone: {
          personality: '',
          communicationStyle: '',
          userExperience: '',
          brandVoice: '',
          emotionalIntelligence: '',
          interactionFlow: ''
        },
        technicalLayer: {
          programmingLanguages: '',
          frameworks: '',
          llmModels: '',
          databases: '',
          apis: '',
          infrastructure: ''
        },
        legalRisk: {
          privacyConcerns: '',
          dataProtection: '',
          compliance: '',
          risks: '',
          mitigation: '',
          ethicalConsiderations: ''
        }
      })),
      
      getModuleData: (module) => {
        const state = get();
        switch (module) {
          case 'project': return state.projectDefinition;
          case 'context': return state.contextMap;
          case 'emotional': return state.emotionalTone;
          case 'technical': return state.technicalLayer;
          case 'legal': return state.legalRisk;
          default: return {};
        }
      },
      
      getAllData: () => {
        const state = get();
        return {
          projectDefinition: state.projectDefinition,
          contextMap: state.contextMap,
          emotionalTone: state.emotionalTone,
          technicalLayer: state.technicalLayer,
          legalRisk: state.legalRisk
        };
      }
    }),
    {
      name: 'contextkit-storage',
      version: 1,
      skipHydration: true
    }
  )
);
