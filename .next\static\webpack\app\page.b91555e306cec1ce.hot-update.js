"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProjectSummaryCard */ \"(app-pages-browser)/./src/components/ProjectSummaryCard.tsx\");\n/* harmony import */ var _components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SmartRecommendations */ \"(app-pages-browser)/./src/components/SmartRecommendations.tsx\");\n/* harmony import */ var _components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProjectStats */ \"(app-pages-browser)/./src/components/ProjectStats.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 \".concat(isArabic ? \"font-arabic\" : \"\"),\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? \"Craftery\" : \"Craftery\",\n                    subtitle: isArabic ? \"منصة ذكية مدعومة بالذكاء الاصطناعي لبناء وتطوير الأفكار الإبداعية\" : \"AI-powered Idea Builder\",\n                    emoji: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDED\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"واجهة متعددة الصفحات\" : \"Multi-page Interface\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"صفحات مخصصة لكل وحدة مع أسئلة ومخرجات مصممة خصيصاً.\" : \"Dedicated pages for each module with tailored prompts and outputs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"✍️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"أسئلة موجهة\" : \"Guided Prompts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أسئلة ذكية لتوجيه عملية تفكيرك عبر كل وحدة.\" : \"Smart questions to guide your thought process through each module.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"نسخ المخرجات\" : \"Output Copying\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أزرار سهلة الاستخدام لنسخ المخرجات الكاملة أو الإجابات الفردية.\" : \"Easy-to-use buttons for copying entire outputs or individual responses.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDFE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"مخرجات منظمة\" : \"Structured Outputs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"مخرجات بصيغة Markdown أو HTML أو JSON للتكامل السهل.\" : \"Outputs in Markdown, HTML, or JSON for easy integration.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"حفظ تلقائي وجلسات\" : \"Auto-save & Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"حفظ واسترجاع تلقائي لجلسات المستخدم للراحة.\" : \"Automatic saving and retrieval of user sessions for convenience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83C\\uDF10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"دعم متعدد اللغات\" : \"Multilingual Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"واجهات باللغتين الإنجليزية والعربية لخدمة جمهور أوسع.\" : \"English and Arabic interfaces to cater to a wider audience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white\",\n                            children: [\n                                \"\\uD83D\\uDEE0️ \",\n                                isArabic ? \"الوحدات المتاحة\" : \"Available Modules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/project-definition\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83C\\uDFAF \",\n                                                isArabic ? \"تعريف المشروع\" : \"Project Definition\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد نطاق مشروعك والمستخدمين والأهداف بأسئلة موجهة.\" : \"Outline the scope, users, and goals of your project with guided prompts.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/interactive\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83E\\uDD16 \",\n                                                isArabic ? \"المساعد التفاعلي\" : \"Interactive Assistant\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تجربة محادثة متقدمة مع الذكاء الاصطناعي مع ميزات تفاعلية حديثة.\" : \"Advanced conversational experience with AI featuring modern interactive capabilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/context-map\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDDFA️ \",\n                                                isArabic ? \"خريطة السياق\" : \"Context Map\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد الوقت واللغة والموقع والجوانب السلوكية لمشروعك.\" : \"Define time, language, location, and behavioral aspects of your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/emotional-tone\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"✨ \",\n                                                isArabic ? \"النبرة والتجربة\" : \"Emotional Tone & Experience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"احدد النبرة العامة وتجربة المستخدم المرغوبة لمشروعك.\" : \"Capture the overall tone and user experience desired for your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/technical-layer\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"⚙️ \",\n                                                isArabic ? \"الطبقة التقنية\" : \"Technical Layer\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد المتطلبات التقنية والأدوات والنماذج المستخدمة.\" : \"Define technical requirements, tools, and models to be used.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/legal-risk\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDD12 \",\n                                                isArabic ? \"التحديات والخصوصية\" : \"Legal & Privacy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تناول التحديات ومخاوف الخصوصية والقضايا التنظيمية.\" : \"Address challenges, privacy concerns, and regulatory issues.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/final-preview\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDCCB \",\n                                                isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"راجع واستخرج السياق الكامل لمشروعك.\" : \"Review and export your complete project context.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/project-definition\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200\",\n                            children: isArabic ? \"ابدأ ببناء السياق\" : \"Start Building Context\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600 dark:text-gray-300\",\n                            children: isArabic ? \"هل أنت مستعد لإنشاء سياق منظم وقابل للتنفيذ لمشاريع الذكاء الاصطناعي؟\" : \"Ready to create structured, actionable context for your AI projects?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});