'use client';

import Header from '@/components/Header';
import ProjectSummaryCard from '@/components/ProjectSummaryCard';
import SmartRecommendations from '@/components/SmartRecommendations';
import ProjectStats from '@/components/ProjectStats';
import ProjectRoadmap from '@/components/ProjectRoadmap';
import { useContextStore } from '@/store/contextStore';

export default function Home() {
  const { currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 ${isArabic ? 'font-arabic' : ''}`} dir={isArabic ? 'rtl' : 'ltr'}>
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <Header
          title={isArabic ? "Craftery" : "Craftery"}
          subtitle={isArabic
            ? "منصة ذكية مدعومة بالذكاء الاصطناعي لبناء وتطوير الأفكار الإبداعية"
            : "AI-powered Idea Builder"
          }
          emoji="🧠"
        />

        {/* Project Summary Card */}
        <div className="mb-8">
          <ProjectSummaryCard />
        </div>

        {/* Smart Recommendations */}
        <div className="mb-8">
          <SmartRecommendations />
        </div>

        {/* Project Statistics */}
        <div className="mb-8">
          <ProjectStats />
        </div>

        {/* Project Roadmap */}
        <div className="mb-12">
          <ProjectRoadmap />
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">🧭</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              {isArabic ? "واجهة متعددة الصفحات" : "Multi-page Interface"}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {isArabic
                ? "صفحات مخصصة لكل وحدة مع أسئلة ومخرجات مصممة خصيصاً."
                : "Dedicated pages for each module with tailored prompts and outputs."
              }
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">✍️</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              {isArabic ? "أسئلة موجهة" : "Guided Prompts"}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {isArabic
                ? "أسئلة ذكية لتوجيه عملية تفكيرك عبر كل وحدة."
                : "Smart questions to guide your thought process through each module."
              }
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">📋</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              {isArabic ? "نسخ المخرجات" : "Output Copying"}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {isArabic
                ? "أزرار سهلة الاستخدام لنسخ المخرجات الكاملة أو الإجابات الفردية."
                : "Easy-to-use buttons for copying entire outputs or individual responses."
              }
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">🧾</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              {isArabic ? "مخرجات منظمة" : "Structured Outputs"}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {isArabic
                ? "مخرجات بصيغة Markdown أو HTML أو JSON للتكامل السهل."
                : "Outputs in Markdown, HTML, or JSON for easy integration."
              }
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">💾</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              {isArabic ? "حفظ تلقائي وجلسات" : "Auto-save & Sessions"}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {isArabic
                ? "حفظ واسترجاع تلقائي لجلسات المستخدم للراحة."
                : "Automatic saving and retrieval of user sessions for convenience."
              }
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <div className="text-3xl mb-4">🌐</div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              {isArabic ? "دعم متعدد اللغات" : "Multilingual Support"}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {isArabic
                ? "واجهات باللغتين الإنجليزية والعربية لخدمة جمهور أوسع."
                : "English and Arabic interfaces to cater to a wider audience."
              }
            </p>
          </div>
        </div>

        {/* Modules Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">
            🛠️ {isArabic ? "الوحدات المتاحة" : "Available Modules"}
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            <a href="/project-definition" className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow block">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                🎯 {isArabic ? "تعريف المشروع" : "Project Definition"}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {isArabic
                  ? "حدد نطاق مشروعك والمستخدمين والأهداف بأسئلة موجهة."
                  : "Outline the scope, users, and goals of your project with guided prompts."
                }
              </p>
            </a>

            <a href="/interactive" className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                🤖 {isArabic ? "المساعد التفاعلي" : "Interactive Assistant"}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {isArabic
                  ? "تجربة محادثة متقدمة مع الذكاء الاصطناعي مع ميزات تفاعلية حديثة."
                  : "Advanced conversational experience with AI featuring modern interactive capabilities."
                }
              </p>
            </a>

            <a href="/context-map" className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                🗺️ {isArabic ? "خريطة السياق" : "Context Map"}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {isArabic
                  ? "حدد الوقت واللغة والموقع والجوانب السلوكية لمشروعك."
                  : "Define time, language, location, and behavioral aspects of your project."
                }
              </p>
            </a>

            <a href="/emotional-tone" className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow block">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                ✨ {isArabic ? "النبرة والتجربة" : "Emotional Tone & Experience"}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {isArabic
                  ? "احدد النبرة العامة وتجربة المستخدم المرغوبة لمشروعك."
                  : "Capture the overall tone and user experience desired for your project."
                }
              </p>
            </a>

            <a href="/technical-layer" className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500 hover:shadow-xl transition-shadow block">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                ⚙️ {isArabic ? "الطبقة التقنية" : "Technical Layer"}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {isArabic
                  ? "حدد المتطلبات التقنية والأدوات والنماذج المستخدمة."
                  : "Define technical requirements, tools, and models to be used."
                }
              </p>
            </a>

            <a href="/legal-risk" className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow block">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                🔒 {isArabic ? "التحديات والخصوصية" : "Legal & Privacy"}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {isArabic
                  ? "تناول التحديات ومخاوف الخصوصية والقضايا التنظيمية."
                  : "Address challenges, privacy concerns, and regulatory issues."
                }
              </p>
            </a>

            <a href="/final-preview" className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500 hover:shadow-xl transition-shadow block">
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-white">
                📋 {isArabic ? "المعاينة النهائية" : "Final Preview"}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {isArabic
                  ? "راجع واستخرج السياق الكامل لمشروعك."
                  : "Review and export your complete project context."
                }
              </p>
            </a>
          </div>
        </section>

        {/* CTA Section */}
        <div className="text-center">
          <a
            href="/project-definition"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200"
          >
            {isArabic ? "ابدأ ببناء السياق" : "Start Building Context"}
          </a>
          <p className="mt-4 text-gray-600 dark:text-gray-300">
            {isArabic
              ? "هل أنت مستعد لإنشاء سياق منظم وقابل للتنفيذ لمشاريع الذكاء الاصطناعي؟"
              : "Ready to create structured, actionable context for your AI projects?"
            }
          </p>
        </div>
      </div>
    </div>
  );
}
